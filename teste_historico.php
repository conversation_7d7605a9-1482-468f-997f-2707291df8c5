<?php
/**
 * Script de teste para verificar se o histórico está exibindo período e área de conhecimento
 */

// Carrega as configurações
require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/Auth.php';
require_once 'includes/Utils.php';
require_once 'includes/functions.php';
require_once 'includes/init.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Função para executar consulta com tratamento de erro
function executarConsulta($db, $sql, $params = [], $default = null) {
    try {
        $result = $db->fetchOne($sql, $params);
        return $result ?: $default;
    } catch (Exception $e) {
        error_log('Erro na consulta SQL: ' . $e->getMessage());
        return $default;
    }
}

// Busca um aluno para teste
$sql_aluno = "SELECT id, nome FROM alunos LIMIT 1";
$aluno_teste = $db->fetchOne($sql_aluno);

if (!$aluno_teste) {
    echo "Nenhum aluno encontrado no banco de dados.";
    exit;
}

echo "<h2>Teste de Histórico Acadêmico</h2>";
echo "<p><strong>Aluno de teste:</strong> {$aluno_teste['nome']} (ID: {$aluno_teste['id']})</p>";

// Inclui a função buscarDadosAlunoCompletoParaDocumento do historico.php
include_once 'historico.php';

// Testa a função
$dados_aluno = buscarDadosAlunoCompletoParaDocumento($db, $aluno_teste['id']);

echo "<h3>Dados do Aluno:</h3>";
echo "<pre>";
print_r($dados_aluno);
echo "</pre>";

// Verifica se os campos necessários estão presentes
echo "<h3>Verificação dos Campos:</h3>";
echo "<ul>";
echo "<li><strong>Nome:</strong> " . ($dados_aluno['nome'] ?? 'NÃO ENCONTRADO') . "</li>";
echo "<li><strong>Curso:</strong> " . ($dados_aluno['curso_nome'] ?? 'NÃO ENCONTRADO') . "</li>";
echo "<li><strong>Data Início Turma:</strong> " . ($dados_aluno['turma_data_inicio'] ?? 'NÃO ENCONTRADO') . "</li>";
echo "<li><strong>Data Fim Turma:</strong> " . ($dados_aluno['turma_data_fim'] ?? 'NÃO ENCONTRADO') . "</li>";
echo "<li><strong>Área Conhecimento ID:</strong> " . ($dados_aluno['area_conhecimento_id'] ?? 'NÃO ENCONTRADO') . "</li>";
echo "<li><strong>Área Conhecimento Nome:</strong> " . ($dados_aluno['area_conhecimento_nome'] ?? 'NÃO ENCONTRADO') . "</li>";
echo "</ul>";

// Verifica se as tabelas necessárias existem
echo "<h3>Verificação das Tabelas:</h3>";

// Verifica tabela turmas
$sql_turmas = "SHOW TABLES LIKE 'turmas'";
$turmas_existe = $db->fetchOne($sql_turmas);
echo "<li><strong>Tabela turmas:</strong> " . ($turmas_existe ? 'EXISTE' : 'NÃO EXISTE') . "</li>";

// Verifica tabela areas_conhecimento
$sql_areas = "SHOW TABLES LIKE 'areas_conhecimento'";
$areas_existe = $db->fetchOne($sql_areas);
echo "<li><strong>Tabela areas_conhecimento:</strong> " . ($areas_existe ? 'EXISTE' : 'NÃO EXISTE') . "</li>";

// Verifica se há dados na tabela areas_conhecimento
if ($areas_existe) {
    $sql_count_areas = "SELECT COUNT(*) as total FROM areas_conhecimento";
    $count_areas = $db->fetchOne($sql_count_areas);
    echo "<li><strong>Registros em areas_conhecimento:</strong> " . ($count_areas['total'] ?? 0) . "</li>";
    
    // Lista algumas áreas
    $sql_list_areas = "SELECT id, nome FROM areas_conhecimento LIMIT 5";
    $areas_list = $db->fetchAll($sql_list_areas);
    echo "<li><strong>Algumas áreas:</strong><ul>";
    foreach ($areas_list as $area) {
        echo "<li>ID {$area['id']}: {$area['nome']}</li>";
    }
    echo "</ul></li>";
}

// Verifica se o curso do aluno tem área de conhecimento
if (!empty($dados_aluno['curso_id'])) {
    $sql_curso = "SELECT id, nome, area_conhecimento_id FROM cursos WHERE id = ?";
    $curso_info = $db->fetchOne($sql_curso, [$dados_aluno['curso_id']]);
    echo "<li><strong>Curso do aluno:</strong> " . ($curso_info['nome'] ?? 'NÃO ENCONTRADO') . "</li>";
    echo "<li><strong>Área do curso:</strong> " . ($curso_info['area_conhecimento_id'] ?? 'NÃO DEFINIDA') . "</li>";
}

// Verifica se o aluno tem turma com datas
if (!empty($dados_aluno['turma_id'])) {
    $sql_turma = "SELECT id, nome, data_inicio, data_fim FROM turmas WHERE id = ?";
    $turma_info = $db->fetchOne($sql_turma, [$dados_aluno['turma_id']]);
    echo "<li><strong>Turma do aluno:</strong> " . ($turma_info['nome'] ?? 'NÃO ENCONTRADA') . "</li>";
    echo "<li><strong>Data início turma:</strong> " . ($turma_info['data_inicio'] ?? 'NÃO DEFINIDA') . "</li>";
    echo "<li><strong>Data fim turma:</strong> " . ($turma_info['data_fim'] ?? 'NÃO DEFINIDA') . "</li>";
}

echo "</ul>";

// Link para gerar o histórico
echo "<h3>Teste de Geração:</h3>";
echo "<p><a href='historico.php?action=gerar_historico&aluno_id={$aluno_teste['id']}' target='_blank'>Gerar Histórico PDF</a></p>";

?>
